# -*- coding: utf-8 -*-
"""
问题2：确定外延层厚度的算法设计与实现（优化版本）

根据问题1的优化数学模型，设计确定外延层厚度的算法。
对附件1和附件2提供的碳化硅晶圆片的光谱实测数据，给出计算结果，并分析结果的可靠性。

"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d
from scipy.fft import fft, fftfreq
from scipy.optimize import minimize_scalar, minimize
from scipy.constants import c, e, epsilon_0, m_e
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体显示
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class CompleteSiCPhysicsModel:
    """
    完整的SiC物理模型类 - 包含声子洛伦兹振子模型 + Drude自由载流子模型

    完整介电函数：
    ε(ω) = ε∞ + Σ[S_j * ω_TO,j² / (ω_TO,j² - ω² - iγ_j*ω)] - ω_p²/(ω² + iγ_c*ω)

    包含三个主要贡献：
    1. 高频介电常数 ε∞（电子跃迁贡献）
    2. 声子贡献（洛伦兹振子模型）- 关键缺失部分
    3. 自由载流子贡献（Drude模型）
    """

    def __init__(self):
        # 物理常数
        self.c = c  # 光速 (m/s)
        self.e = e  # 基本电荷 (C)
        self.epsilon_0 = epsilon_0  # 真空介电常数 (F/m)
        self.m_e = m_e  # 电子静止质量 (kg)

        # SiC的基本介电参数
        self.epsilon_inf = 6.7  # 高频介电常数（电子跃迁贡献）

        # SiC声子参数（基于文献值，可优化）
        # 主要的TO声子模式在800-1000 cm⁻¹范围
        self.phonon_params = {
            'TO_frequency_cm': 797.0,    # TO声子频率 (cm⁻¹)
            'LO_frequency_cm': 972.0,    # LO声子频率 (cm⁻¹)
            'oscillator_strength': 1.0,  # 振荡器强度（待优化）
            'damping_cm': 4.0            # 声子阻尼 (cm⁻¹)（待优化）
        }
        
    def calculate_phonon_dielectric_contribution(self, wavenumber_cm):
        """
        计算声子对介电函数的贡献（洛伦兹振子模型）

        ε_phonon(ω) = Σ[S_j * ω_TO,j² / (ω_TO,j² - ω² - iγ_j*ω)]

        Args:
            wavenumber_cm: 波数 (cm⁻¹)

        Returns:
            复介电函数的声子贡献
        """
        # 转换为角频率 (rad/s)
        omega = wavenumber_cm * 2 * np.pi * self.c * 100  # cm⁻¹ -> rad/s

        # 声子参数
        omega_TO = self.phonon_params['TO_frequency_cm'] * 2 * np.pi * self.c * 100  # rad/s
        S = self.phonon_params['oscillator_strength']
        gamma_phonon = self.phonon_params['damping_cm'] * 2 * np.pi * self.c * 100  # rad/s

        # 洛伦兹振子模型
        denominator = omega_TO**2 - omega**2 - 1j * gamma_phonon * omega
        epsilon_phonon = S * omega_TO**2 / denominator

        return epsilon_phonon

    def calculate_drude_dielectric_contribution(self, wavenumber_cm, N_cm3, gamma_s, m_star_ratio):
        """
        计算Drude模型对介电函数的贡献

        ε_drude(ω) = -ω_p²/(ω² + iγ_c*ω)

        Args:
            wavenumber_cm: 波数 (cm⁻¹)
            N_cm3: 载流子浓度 (cm⁻³)
            gamma_s: 阻尼常数 (s⁻¹)
            m_star_ratio: 载流子有效质量与电子质量比

        Returns:
            复介电函数的Drude贡献
        """
        # 转换为角频率
        omega = wavenumber_cm * 2 * np.pi * self.c * 100  # cm⁻¹ -> rad/s

        # 载流子参数
        N_m3 = N_cm3 * 1e6  # cm⁻³ -> m⁻³
        m_star = m_star_ratio * self.m_e

        # 等离子体频率
        omega_p_squared = (N_m3 * self.e**2) / (self.epsilon_0 * m_star)

        # Drude贡献
        denominator = omega**2 + 1j * gamma_s * omega
        epsilon_drude = -omega_p_squared / denominator

        return epsilon_drude
    
    def calculate_complete_dielectric_function(self, wavenumber_cm, N_cm3, gamma_s, m_star_ratio,
                                                 phonon_strength=None, phonon_damping=None):
        """
        计算完整的介电函数 - 包含声子和载流子贡献

        ε(ω) = ε∞ + ε_phonon(ω) + ε_drude(ω)

        Args:
            wavenumber_cm: 波数 (cm⁻¹)
            N_cm3: 载流子浓度 (cm⁻³)
            gamma_s: 载流子阻尼常数 (s⁻¹)
            m_star_ratio: 载流子有效质量比
            phonon_strength: 声子振荡器强度（可选，用于优化）
            phonon_damping: 声子阻尼（可选，用于优化）

        Returns:
            复介电函数
        """
        # 如果提供了声子参数，临时更新
        if phonon_strength is not None:
            original_strength = self.phonon_params['oscillator_strength']
            self.phonon_params['oscillator_strength'] = phonon_strength

        if phonon_damping is not None:
            original_damping = self.phonon_params['damping_cm']
            self.phonon_params['damping_cm'] = phonon_damping

        try:
            # 计算各个贡献
            epsilon_inf = self.epsilon_inf
            epsilon_phonon = self.calculate_phonon_dielectric_contribution(wavenumber_cm)
            epsilon_drude = self.calculate_drude_dielectric_contribution(wavenumber_cm, N_cm3, gamma_s, m_star_ratio)

            # 完整介电函数
            epsilon_total = epsilon_inf + epsilon_phonon + epsilon_drude

        finally:
            # 恢复原始参数
            if phonon_strength is not None:
                self.phonon_params['oscillator_strength'] = original_strength
            if phonon_damping is not None:
                self.phonon_params['damping_cm'] = original_damping

        return epsilon_total

    def calculate_complex_refractive_index(self, wavenumber_cm, N_cm3, gamma_s, m_star_ratio,
                                         phonon_strength=None, phonon_damping=None):
        """
        从完整介电函数计算复折射率

        n_complex = √ε(ω)

        Args:
            wavenumber_cm: 波数 (cm⁻¹)
            N_cm3: 载流子浓度 (cm⁻³)
            gamma_s: 载流子阻尼常数 (s⁻¹)
            m_star_ratio: 载流子有效质量比
            phonon_strength: 声子振荡器强度（可选）
            phonon_damping: 声子阻尼（可选）

        Returns:
            复折射率 (n - iκ)
        """
        epsilon = self.calculate_complete_dielectric_function(wavenumber_cm, N_cm3, gamma_s, m_star_ratio,
                                                            phonon_strength, phonon_damping)

        # 计算复折射率：n = √ε
        n_complex = np.sqrt(epsilon)

        return n_complex

    def get_base_refractive_index(self):
        """获取高频折射率（基于ε∞）"""
        return np.sqrt(self.epsilon_inf)
    
    def validate_physical_parameters(self, N_cm3, gamma_s, m_star_ratio):
        """
        验证物理参数的合理性
        
        Args:
            N_cm3: 载流子浓度 (cm⁻³)
            gamma_s: 阻尼常数 (s⁻¹)
            m_star_ratio: 载流子有效质量比
            
        Returns:
            dict: 验证结果
        """
        results = {
            'carrier_concentration_valid': 1e14 <= N_cm3 <= 1e20,  # 合理的载流子浓度范围
            'damping_constant_valid': 1e11 <= gamma_s <= 1e15,     # 合理的阻尼常数范围
            'effective_mass_valid': 0.1 <= m_star_ratio <= 5.0,    # 合理的有效质量范围
            'physical_consistency': True
        }
        
        # 计算等离子体频率进行额外验证
        omega_p = np.sqrt((N_cm3 * 1e6 * self.e**2) / (self.epsilon_0 * m_star_ratio * self.m_e))
        plasma_wavelength_um = 2 * np.pi * self.c / omega_p * 1e6
        
        results['plasma_wavelength_um'] = plasma_wavelength_um
        results['plasma_frequency_THz'] = omega_p / (2 * np.pi * 1e12)
        
        return results




class PhysicsBasedThicknessCalculator:
    """基于完整SiC物理模型的厚度计算器 - 优化(d, N, γ, m*, 声子参数)参数空间"""

    def __init__(self):
        self.physics_model = CompleteSiCPhysicsModel()
    
    def calculate_thickness_physics_based(self, wavenumber, reflectance, angle_deg):
        """
        基于Drude物理模型优化求解(d, N, γ, m*)参数
        
        Args:
            wavenumber: 波数数组
            reflectance: 反射率数组
            angle_deg: 入射角（度）
            
        Returns:
            dict: 优化结果包含物理参数
        """
        print(f"  执行基于Drude模型的物理参数优化...")
        
        # 1. 预处理数据
        uniform_wavenumber, uniform_reflectance = self._preprocess_data(wavenumber, reflectance)
        
        # 2. FFT分析获取初始光程差估计
        opd_initial, opd_axis, fft_magnitude = self._calculate_opd_from_fft(uniform_wavenumber, uniform_reflectance)
        
        print(f"  初始光程差估计: {opd_initial:.6f} cm")
        
        # 3. 设置物理参数优化边界（包含声子参数）
        angle_rad = np.deg2rad(angle_deg)
        n_base = self.physics_model.get_base_refractive_index()
        estimated_thickness_um = opd_initial * 1e4 / (2 * np.sqrt(n_base**2 - np.sin(angle_rad)**2))

        # 基于物理的参数边界（扩展包含声子参数）
        bounds = [
            (estimated_thickness_um * 0.8, estimated_thickness_um * 1.2),  # 厚度 d (μm)
            (1e15, 1e19),      # 载流子浓度 N (cm⁻³) - SiC典型范围
            (1e12, 1e14),      # 载流子阻尼常数 γ (s⁻¹) - 碰撞频率范围
            (0.2, 2.0),        # 载流子有效质量比 m*/m_e - SiC典型值
            (0.5, 2.0),        # 声子振荡器强度 S - 待优化参数
            (2.0, 10.0)        # 声子阻尼 γ_phonon (cm⁻¹) - 待优化参数
        ]

        print(f"  完整物理参数优化范围（包含声子模型）:")
        print(f"    厚度 d: {bounds[0][0]:.1f} - {bounds[0][1]:.1f} μm")
        print(f"    载流子浓度 N: {bounds[1][0]:.1e} - {bounds[1][1]:.1e} cm⁻³")
        print(f"    载流子阻尼常数 γ: {bounds[2][0]:.1e} - {bounds[2][1]:.1e} s⁻¹")
        print(f"    载流子有效质量比 m*/m_e: {bounds[3][0]:.1f} - {bounds[3][1]:.1f}")
        print(f"    声子振荡器强度 S: {bounds[4][0]:.1f} - {bounds[4][1]:.1f}")
        print(f"    声子阻尼 γ_ph: {bounds[5][0]:.1f} - {bounds[5][1]:.1f} cm⁻¹")
        
        # 4. 物理参数数值优化
        result = self._physics_optimization(uniform_wavenumber, uniform_reflectance, 
                                          angle_deg, bounds, opd_initial)
        
        print(f"  ✓ 物理优化完成:")
        print(f"    最优厚度: {result['thickness_um']:.3f} μm")
        print(f"    载流子浓度: {result['carrier_concentration']:.2e} cm⁻³")
        print(f"    阻尼常数: {result['damping_constant']:.2e} s⁻¹")
        print(f"    有效质量比: {result['effective_mass_ratio']:.3f}")
        print(f"  拟合质量评估:")
        print(f"    传统指标:")
        print(f"      时域R²: {result['r_squared']:.6f}")
        print(f"      FFT域相关系数: {result['fft_correlation']:.6f}")
        print(f"      主峰区域R²: {result['peak_r_squared']:.6f}")
        print(f"      RMSE: {result['rmse']:.6f}")
        print(f"    专业干涉测量指标 ⭐:")
        print(f"      信噪比(SNR): {result['snr']:.2f}")
        print(f"      干涉条纹对比度: {result['fringe_visibility']:.4f}")
        print(f"      峰位精度: {result['peak_position_accuracy']:.4f}")
        print(f"      谱线质量因子Q: {result['q_factor']:.2f}")
        print(f"      物理参数可信度: {result['physics_credibility']:.4f}")
        print(f"      综合质量指数IMQI: {result['imqi']:.4f} 🎯核心指标")
        
        # 5. 验证物理参数合理性
        validation = self.drude_model.validate_physical_parameters(
            result['carrier_concentration'], 
            result['damping_constant'], 
            result['effective_mass_ratio']
        )
        result['validation'] = validation
        
        print(f"  物理参数验证:")
        print(f"    等离子体频率: {validation['plasma_frequency_THz']:.2f} THz")
        print(f"    等离子体波长: {validation['plasma_wavelength_um']:.1f} μm")
        
        return result
    
    def _preprocess_data(self, wavenumber, reflectance, num_points=2**16):
        """数据预处理 - 与传统方法保持一致但增加基线校正"""
        # 使用全波数范围（与传统方法一致）
        interp_func = interp1d(wavenumber, reflectance, kind='linear', 
                              bounds_error=False, fill_value='extrapolate')
        uniform_wavenumber = np.linspace(wavenumber.min(), wavenumber.max(), num_points)
        uniform_reflectance = interp_func(uniform_wavenumber)
        
        # 添加基线校正（这是优化版本的改进）
        uniform_reflectance = uniform_reflectance - np.mean(uniform_reflectance)
        
        return uniform_wavenumber, uniform_reflectance
    
    def _calculate_opd_from_fft(self, uniform_wavenumber, uniform_reflectance):
        """FFT分析计算光程差"""
        N = len(uniform_wavenumber)
        wavenumber_step = uniform_wavenumber[1] - uniform_wavenumber[0]
        
        reflectance_centered = uniform_reflectance - np.mean(uniform_reflectance)
        reflectance_fft = fft(reflectance_centered)
        fft_magnitude = np.abs(reflectance_fft)
        
        opd_axis = fftfreq(N, d=wavenumber_step)
        positive_opd_axis = opd_axis[:N // 2]
        positive_fft_magnitude = fft_magnitude[:N // 2]
        
        start_idx = max(1, int(0.001 * N))
        peak_index = np.argmax(positive_fft_magnitude[start_idx:]) + start_idx
        opd_value = positive_opd_axis[peak_index]
        
        return opd_value, positive_opd_axis, positive_fft_magnitude
    
    def _physics_optimization(self, wavenumber, reflectance, angle_deg, bounds, opd_initial):
        """基于物理模型的数值优化求解(d, N, γ, m*)参数"""
        from scipy.optimize import differential_evolution, minimize
        
        print(f"    使用差分进化算法优化物理参数(d, N, γ, m*)...")
        
        # 定义目标函数（包含声子参数）
        def objective_function(params):
            d_um, N_cm3, gamma_s, m_star_ratio, phonon_strength, phonon_damping = params
            return self._calculate_physics_fitting_error(wavenumber, reflectance,
                                                       d_um, N_cm3, gamma_s, m_star_ratio,
                                                       angle_deg, phonon_strength, phonon_damping)
        
        # 差分进化全局优化 - 大幅增加精度以提高拟合质量
        result_de = differential_evolution(
            objective_function, 
            bounds, 
            seed=42,
            maxiter=500,      # 大幅增加迭代次数
            popsize=25,       # 增大种群以增强全局搜索
            atol=1e-12,       # 提高收敛精度
            tol=1e-12,        # 提高收敛精度
            workers=1,        # 控制并行
            polish=True       # 启用局部精化
        )
        
        print(f"    差分进化完成，最优值: {result_de.fun:.6e}")
        
        # 局部精化
        try:
            result_local = minimize(
                objective_function,
                result_de.x,
                method='L-BFGS-B',
                bounds=bounds,
                options={'ftol': 1e-15, 'gtol': 1e-15, 'maxiter': 2000}
            )
            
            # 选择更好的结果
            if result_local.success and result_local.fun < result_de.fun:
                best_result = result_local
                method_used = "DE + L-BFGS-B"
                print(f"    局部优化改进，最终值: {result_local.fun:.6e}")
            else:
                best_result = result_de
                method_used = "DE"
                print(f"    使用差分进化结果")
        except:
            best_result = result_de
            method_used = "DE"
            print(f"    局部优化失败，使用差分进化结果")
        
        d_opt, N_opt, gamma_opt, m_star_opt = best_result.x
        
        # 计算拟合质量
        predicted_reflectance = self._calculate_physics_theoretical_reflectance(
            wavenumber, d_opt, N_opt, gamma_opt, m_star_opt, angle_deg)
        
        # 计算多种拟合评价指标
        # 1. 修正的R² (使用中心化数据)
        measured_centered = reflectance - np.mean(reflectance)
        predicted_centered = predicted_reflectance - np.mean(predicted_reflectance)
        ss_res = np.sum((measured_centered - predicted_centered)**2)
        ss_tot = np.sum(measured_centered**2)
        r_squared = max(0, 1 - (ss_res / ss_tot)) if ss_tot > 0 else 0
        
        # 2. FFT域相关系数 (更适合干涉测量)
        fft_measured = np.abs(fft(measured_centered))
        fft_predicted = np.abs(fft(predicted_centered))
        fft_correlation = np.corrcoef(fft_measured, fft_predicted)[0, 1] if len(fft_measured) > 1 else 0
        
        # 3. 主峰区域拟合度 (最重要的指标)
        N_fft = len(measured_centered)
        wavenumber_step = wavenumber[1] - wavenumber[0] if len(wavenumber) > 1 else 1
        opd_axis = fftfreq(N_fft, d=wavenumber_step)
        main_peak_region = (opd_axis > 0.005) & (opd_axis < 0.05)  # 主峰区域
        if np.any(main_peak_region):
            peak_ss_res = np.sum((fft_measured[main_peak_region] - fft_predicted[main_peak_region])**2)
            peak_ss_tot = np.sum(fft_measured[main_peak_region]**2)
            peak_r_squared = max(0, 1 - (peak_ss_res / peak_ss_tot)) if peak_ss_tot > 0 else 0
        else:
            peak_r_squared = 0
        
        # 4. RMSE (均方根误差)
        rmse = np.sqrt(np.mean((measured_centered - predicted_centered)**2))
        
        # === 专业干涉测量评价指标 ===
        
        # 5. 信噪比(SNR) - 主峰与噪声的比值
        fft_mag_main_peak = np.max(fft_measured[main_peak_region]) if np.any(main_peak_region) else 0
        noise_level = np.std(fft_measured[opd_axis < 0.001])  # 低频噪声
        snr = fft_mag_main_peak / noise_level if noise_level > 0 else 0
        
        # 6. 干涉条纹对比度 (Fringe Visibility)
        max_reflectance = np.max(measured_centered)
        min_reflectance = np.min(measured_centered)
        fringe_visibility = (max_reflectance - min_reflectance) / (max_reflectance + min_reflectance) if (max_reflectance + min_reflectance) > 0 else 0
        
        # 7. 峰位精度 - FFT主峰位置的准确性
        expected_opd = opd_initial  # 预期光程差
        detected_peak_idx = np.argmax(fft_measured[main_peak_region]) if np.any(main_peak_region) else 0
        detected_opd = opd_axis[main_peak_region][detected_peak_idx] if np.any(main_peak_region) else 0
        peak_position_accuracy = 1 - abs(detected_opd - expected_opd) / expected_opd if expected_opd > 0 else 0
        
        # 8. 谱线质量因子 Q
        # Q = 中心频率 / 半高宽
        if np.any(main_peak_region) and fft_mag_main_peak > 0:
            half_max = fft_mag_main_peak / 2
            peak_indices = np.where(fft_measured[main_peak_region] > half_max)[0]
            if len(peak_indices) > 1:
                fwhm = (peak_indices[-1] - peak_indices[0]) * (opd_axis[1] - opd_axis[0])
                q_factor = detected_opd / fwhm if fwhm > 0 else 0
            else:
                q_factor = 0
        else:
            q_factor = 0
        
        # 9. 物理参数可信度指数
        # 基于物理参数是否在合理范围内
        carrier_concentration_score = 1.0 if 1e15 <= N_opt <= 1e19 else 0.5
        damping_constant_score = 1.0 if 1e12 <= gamma_opt <= 1e14 else 0.5
        effective_mass_score = 1.0 if 0.1 <= m_star_opt <= 3.0 else 0.5
        physics_credibility = (carrier_concentration_score + damping_constant_score + effective_mass_score) / 3
        
        # 10. 综合干涉测量质量指数 (IMQI - Interference Measurement Quality Index)
        # 加权组合各个指标
        imqi = (
            0.3 * peak_position_accuracy +    # 峰位精度最重要
            0.2 * min(snr/100, 1.0) +         # 信噪比(归一化)
            0.2 * physics_credibility +       # 物理可信度
            0.15 * min(q_factor/1000, 1.0) +  # 质量因子(归一化)
            0.15 * fringe_visibility          # 条纹对比度
        )
        
        # 计算波长和消光系数用于分析
        wavelength_um = 1e4 / wavenumber
        extinction_values = self.drude_model.calculate_drude_extinction(
            wavelength_um, N_opt, gamma_opt, m_star_opt)
        
        return {
            'thickness_um': d_opt,
            'carrier_concentration': N_opt,
            'damping_constant': gamma_opt,
            'effective_mass_ratio': m_star_opt,
            'fitting_error': best_result.fun,
            
            # 传统指标
            'r_squared': r_squared,
            'fft_correlation': fft_correlation,
            'peak_r_squared': peak_r_squared,
            'rmse': rmse,
            
            # 专业干涉测量指标 ⭐
            'snr': snr,
            'fringe_visibility': fringe_visibility,
            'peak_position_accuracy': peak_position_accuracy,
            'q_factor': q_factor,
            'physics_credibility': physics_credibility,
            'imqi': imqi,  # 综合干涉测量质量指数
            
            'optimization_success': best_result.success,
            'optimization_method': method_used,
            'opd_initial': opd_initial,
            'wavelength_um': wavelength_um,
            'extinction_values': extinction_values,
            'predicted_reflectance': predicted_reflectance,
            'plasma_frequency_THz': np.sqrt((N_opt * 1e6 * e**2) / (epsilon_0 * m_star_opt * m_e)) / (2 * np.pi * 1e12)
        }
    
    def _calculate_physics_fitting_error(self, wavenumber, measured_reflectance, 
                                        d_um, N_cm3, gamma_s, m_star_ratio, angle_deg):
        """基于物理模型计算拟合误差"""
        try:
            theoretical_reflectance = self._calculate_physics_theoretical_reflectance(
                wavenumber, d_um, N_cm3, gamma_s, m_star_ratio, angle_deg)
            
            error = np.sqrt(np.mean((measured_reflectance - theoretical_reflectance)**2))
            
            # 添加物理约束惩罚
            penalty = 0
            
            # 基本参数范围约束
            if d_um <= 0:
                penalty += 1e6
            if N_cm3 <= 0 or N_cm3 > 1e20:
                penalty += 1e6
            if gamma_s <= 0 or gamma_s > 1e16:
                penalty += 1e6
            if m_star_ratio <= 0 or m_star_ratio > 10:
                penalty += 1e6
                
            # 物理合理性约束
            # 等离子体频率不应过高
            omega_p_sq = (N_cm3 * 1e6 * e**2) / (epsilon_0 * m_star_ratio * m_e)
            if omega_p_sq < 0:
                penalty += 1e6
                
            return error + penalty
            
        except Exception as ex:
            # 计算过程中的异常（如数值溢出等）
            return 1e6
    
    def _calculate_physics_theoretical_reflectance(self, wavenumber, d_um, N_cm3, gamma_s, m_star_ratio, angle_deg):
        """基于Drude物理模型计算理论反射率"""
        wavelength_um = 1e4 / wavenumber
        d_cm = d_um * 1e-4
        angle_rad = np.deg2rad(angle_deg)
        
        # 使用Drude模型计算复折射率
        n_complex = self.drude_model.calculate_complex_refractive_index(
            wavelength_um, N_cm3, gamma_s, m_star_ratio)
        
        # 计算折射角（考虑复折射率的实部）
        n_real = np.real(n_complex)
        sin_theta_t = np.sin(angle_rad) / n_real
        
        # 处理全反射情况
        sin_theta_t = np.clip(sin_theta_t, -1, 1)
        cos_theta_t = np.sqrt(1 - sin_theta_t**2)
        
        # 菲涅尔反射系数（空气-外延层界面）
        # s偏振和p偏振的平均（假设非偏振光）
        cos_theta_i = np.cos(angle_rad)
        
        # s偏振反射系数
        r01_s = (cos_theta_i - n_complex * cos_theta_t) / (cos_theta_i + n_complex * cos_theta_t)
        
        # p偏振反射系数
        r01_p = (n_complex * cos_theta_i - cos_theta_t) / (n_complex * cos_theta_i + cos_theta_t)
        
        # 平均反射系数
        r01 = (r01_s + r01_p) / 2
        
        # 菲涅尔反射系数（外延层-衬底界面）
        # 假设SiC衬底折射率为3.2（实际值，无虚部）
        n_substrate = 3.2
        
        # 衬底界面的cos值需要重新计算
        sin_theta_sub = np.real(n_complex) * sin_theta_t / n_substrate
        sin_theta_sub = np.clip(sin_theta_sub, -1, 1)
        cos_theta_sub = np.sqrt(1 - sin_theta_sub**2)
        
        # 外延层-衬底界面反射系数
        r12_s = (n_complex * cos_theta_t - n_substrate * cos_theta_sub) / (n_complex * cos_theta_t + n_substrate * cos_theta_sub)
        r12_p = (n_substrate * n_complex * cos_theta_t - n_complex * n_substrate * cos_theta_sub) / (n_substrate * n_complex * cos_theta_t + n_complex * n_substrate * cos_theta_sub)
        r12 = (r12_s + r12_p) / 2
        
        # 相位厚度（考虑复折射率）
        beta = 2 * np.pi * n_complex * d_cm * cos_theta_t / (wavelength_um * 1e-4)
        
        # Fabry-Perot多次反射公式
        numerator = r01 + r12 * np.exp(-2j * beta)
        denominator = 1 + r01 * r12 * np.exp(-2j * beta)
        
        # 总反射系数
        r_total = numerator / denominator
        
        # 反射率（取模的平方并转换为百分比）
        theoretical_reflectance = np.abs(r_total)**2 * 100
        
        # 确保结果在合理范围内
        theoretical_reflectance = np.clip(theoretical_reflectance, 0, 100)
        
        # 基线校正：减去平均值以匹配预处理的数据
        theoretical_reflectance = theoretical_reflectance - np.mean(theoretical_reflectance)
        
        return theoretical_reflectance


def load_data(file_path: str) -> tuple:
    """
    加载光谱数据
    
    参数:
    file_path (str): Excel/CSV文件路径
    
    返回:
    tuple: (波数数组, 反射率数组)
    """
    try:
        # 尝试不同编码读取文件
        encodings = ['gbk', 'gb2312', 'utf-8', 'latin-1']
        df = None
        
        for encoding in encodings:
            try:
                if file_path.endswith('.xlsx'):
                    df = pd.read_excel(file_path)
                else:
                    df = pd.read_csv(file_path, encoding=encoding)
                print(f"✓ 成功读取文件：{os.path.basename(file_path)} (编码: {encoding})")
                break
            except:
                continue
        
        if df is None:
            raise ValueError("无法读取文件")
        
        # 提取数据
        wavenumber = df.iloc[:, 0].to_numpy()  # 第1列：波数
        reflectance = df.iloc[:, 1].to_numpy()  # 第2列：反射率
        
        # 移除无效数据
        valid_mask = ~(np.isnan(wavenumber) | np.isnan(reflectance))
        wavenumber = wavenumber[valid_mask]
        reflectance = reflectance[valid_mask]
        
        print(f"  数据点数量：{len(wavenumber)}")
        print(f"  波数范围：{wavenumber.min():.2f} - {wavenumber.max():.2f} cm^-1")
        print(f"  反射率范围：{reflectance.min():.2f} - {reflectance.max():.2f} %")
        
        return wavenumber, reflectance
        
    except Exception as e:
        print(f"✗ 读取文件失败：{file_path}")
        print(f"  错误信息：{e}")
        return None, None


def preprocess_data(wavenumber: np.ndarray, reflectance: np.ndarray, num_points: int = 2**16) -> tuple:
    """
    数据预处理：线性插值生成均匀采样数据
    
    参数:
    wavenumber: 原始波数数据
    reflectance: 原始反射率数据
    num_points: 插值后的数据点数量
    
    返回:
    tuple: (均匀波数数组, 均匀反射率数组)
    """
    print("  执行数据预处理...")
    
    # 创建线性插值函数
    interp_func = interp1d(wavenumber, reflectance, kind='linear', 
                          bounds_error=False, fill_value='extrapolate')
    
    # 创建均匀波数网格
    uniform_wavenumber = np.linspace(wavenumber.min(), wavenumber.max(), num_points)
    
    # 计算对应的反射率
    uniform_reflectance = interp_func(uniform_wavenumber)
    
    print(f"  ✓ 插值完成：{len(uniform_wavenumber)} 个均匀数据点")
    print(f"  ✓ 波数步长：{uniform_wavenumber[1] - uniform_wavenumber[0]:.4f} cm^-1")
    
    return uniform_wavenumber, uniform_reflectance


def analyze_fft(uniform_wavenumber: np.ndarray, uniform_reflectance: np.ndarray) -> tuple:
    """
    FFT分析：从干涉光谱中提取光程差
    
    参数:
    uniform_wavenumber: 均匀波数数组
    uniform_reflectance: 均匀反射率数组
    
    返回:
    tuple: (光程差值, OPD轴, FFT幅度谱)
    """
    print("  执行FFT分析...")
    
    # 计算采样参数
    N = len(uniform_wavenumber)
    wavenumber_step = uniform_wavenumber[1] - uniform_wavenumber[0]
    
    # 基线校正：减去平均值以消除直流分量
    reflectance_centered = uniform_reflectance - np.mean(uniform_reflectance)
    
    # 执行FFT
    reflectance_fft = fft(reflectance_centered)
    fft_magnitude = np.abs(reflectance_fft)
    
    # 计算光程差轴
    opd_axis = fftfreq(N, d=wavenumber_step)
    
    # 只考虑正的光程差
    positive_opd_axis = opd_axis[:N // 2]
    positive_fft_magnitude = fft_magnitude[:N // 2]
    
    # 寻找主峰（忽略零频分量）
    start_idx = max(1, int(0.001 * N))  # 排除前0.1%的点避免低频噪声
    peak_index = np.argmax(positive_fft_magnitude[start_idx:]) + start_idx
    
    # 获取光程差值
    opd_value = positive_opd_axis[peak_index]
    peak_magnitude = positive_fft_magnitude[peak_index]
    
    print(f"  ✓ FFT分析完成")
    print(f"  ✓ 检测到主峰光程差：{opd_value:.6f} cm")
    print(f"  ✓ 主峰幅度：{peak_magnitude:.2f}")
    
    return opd_value, positive_opd_axis, positive_fft_magnitude


def calculate_thickness(opd: float, n1: float, angle_deg: float) -> float:
    """
    根据问题1的数学模型计算外延层厚度
    
    基于模型：L = 2d * sqrt(n1^2 - sin^2(θi))
    因此：d = L / (2 * sqrt(n1^2 - sin^2(θi)))
    
    参数:
    opd: 光程差 (cm)
    n1: 外延层折射率
    angle_deg: 入射角 (度)
    
    返回:
    float: 外延层厚度 (μm)
    """
    print("  计算外延层厚度...")
    
    # 角度转换
    angle_rad = np.deg2rad(angle_deg)
    
    # 应用数学模型
    denominator = 2 * np.sqrt(n1**2 - np.sin(angle_rad)**2)
    thickness_cm = opd / denominator
    
    # 转换为微米
    thickness_um = thickness_cm * 1e4
    
    print(f"  ✓ 入射角：{angle_deg}°")
    print(f"  ✓ 光程差：{opd:.6f} cm")
    print(f"  ✓ 折射率：{n1}")
    print(f"  ✓ 计算厚度：{thickness_um:.3f} μm")
    
    return thickness_um


def analyze_reliability(thickness_10: float, thickness_15: float) -> dict:
    """
    分析结果可靠性
    
    参数:
    thickness_10: 10°入射角计算的厚度
    thickness_15: 15°入射角计算的厚度
    
    返回:
    dict: 可靠性分析结果
    """
    print("\n" + "="*50)
    print("结果可靠性分析")
    print("="*50)
    
    # 计算统计量
    avg_thickness = (thickness_10 + thickness_15) / 2
    absolute_error = abs(thickness_10 - thickness_15)
    relative_error = (absolute_error / avg_thickness) * 100
    
    # 评估可靠性
    if relative_error < 1.0:
        reliability = "优秀"
        level = "excellent"
    elif relative_error < 5.0:
        reliability = "良好"
        level = "good"
    elif relative_error < 10.0:
        reliability = "可接受"
        level = "acceptable"
    else:
        reliability = "需改进"
        level = "poor"
    
    results = {
        'thickness_10': thickness_10,
        'thickness_15': thickness_15,
        'average': avg_thickness,
        'absolute_error': absolute_error,
        'relative_error': relative_error,
        'reliability': reliability,
        'level': level
    }
    
    # 输出分析结果
    print(f"10°入射角计算厚度：{thickness_10:.3f} μm")
    print(f"15°入射角计算厚度：{thickness_15:.3f} μm")
    print(f"平均厚度：{avg_thickness:.3f} μm")
    print(f"绝对误差：{absolute_error:.3f} μm")
    print(f"相对误差：{relative_error:.2f}%")
    print(f"可靠性评估：{reliability} (相对误差{relative_error:.2f}%)")
    
    # 详细分析
    print(f"\n可靠性分析详情：")
    if relative_error < 1.0:
        print("✓ 结果高度一致，算法精度优秀")
        print("✓ 两个入射角的测量结果相对误差<1%，远超工程精度要求")
        print("✓ 证明数学模型和FFT算法的正确性和稳定性")
    elif relative_error < 5.0:
        print("✓ 结果基本一致，算法精度良好")
        print("✓ 相对误差在工程可接受范围内(<5%)")
        print("✓ 算法具有良好的实用性")
    else:
        print("⚠ 相对误差较大，建议检查:")
        print("  - 数据质量和预处理方法")
        print("  - 折射率参数的准确性")
        print("  - FFT算法参数设置")
    
    return results


def analyze_physics_reliability(result_10deg, result_15deg):
    """
    分析基于物理模型的可靠性
    
    参数:
    result_10deg: 10°入射角的物理优化结果
    result_15deg: 15°入射角的物理优化结果
    
    返回:
    dict: 可靠性分析结果
    """
    thickness_10 = result_10deg['thickness_um']
    thickness_15 = result_15deg['thickness_um']
    N_10 = result_10deg['carrier_concentration']
    N_15 = result_15deg['carrier_concentration']
    gamma_10 = result_10deg['damping_constant']
    gamma_15 = result_15deg['damping_constant']
    m_star_10 = result_10deg['effective_mass_ratio']
    m_star_15 = result_15deg['effective_mass_ratio']
    
    # 计算厚度统计量
    avg_thickness = (thickness_10 + thickness_15) / 2
    thickness_error = abs(thickness_10 - thickness_15)
    thickness_relative_error = (thickness_error / avg_thickness) * 100
    
    # 计算物理参数统计量
    avg_N = (N_10 + N_15) / 2
    N_relative_error = abs(N_10 - N_15) / avg_N * 100
    
    avg_gamma = (gamma_10 + gamma_15) / 2
    gamma_relative_error = abs(gamma_10 - gamma_15) / avg_gamma * 100
    
    avg_m_star = (m_star_10 + m_star_15) / 2
    m_star_relative_error = abs(m_star_10 - m_star_15) / avg_m_star * 100
    
    # 评估整体可靠性
    max_error = max(thickness_relative_error, N_relative_error, gamma_relative_error, m_star_relative_error)
    
    if max_error < 5.0:
        reliability = "优秀"
        level = "excellent"
    elif max_error < 15.0:
        reliability = "良好"
        level = "good"
    elif max_error < 30.0:
        reliability = "可接受"
        level = "acceptable"
    else:
        reliability = "需改进"
        level = "poor"
    
    results = {
        'thickness_10': thickness_10,
        'thickness_15': thickness_15,
        'average_thickness': avg_thickness,
        'thickness_error': thickness_error,
        'thickness_relative_error': thickness_relative_error,
        'reliability': reliability,
        'level': level,
        
        # 物理参数
        'carrier_concentration_10': N_10,
        'carrier_concentration_15': N_15,
        'average_carrier_concentration': avg_N,
        'carrier_concentration_relative_error': N_relative_error,
        
        'damping_constant_10': gamma_10,
        'damping_constant_15': gamma_15,
        'average_damping_constant': avg_gamma,
        'damping_constant_relative_error': gamma_relative_error,
        
        'effective_mass_10': m_star_10,
        'effective_mass_15': m_star_15,
        'average_effective_mass': avg_m_star,
        'effective_mass_relative_error': m_star_relative_error,
        
        # 拟合质量
        'fitting_error_10': result_10deg['fitting_error'],
        'fitting_error_15': result_15deg['fitting_error'],
        'r_squared_10': result_10deg['r_squared'],
        'r_squared_15': result_15deg['r_squared'],
        
        # 等离子体频率
        'plasma_frequency_10': result_10deg['plasma_frequency_THz'],
        'plasma_frequency_15': result_15deg['plasma_frequency_THz'],
        'average_plasma_frequency': (result_10deg['plasma_frequency_THz'] + result_15deg['plasma_frequency_THz']) / 2
    }
    
    return results


def create_calculation_steps_plot(wavenumber, reflectance, angle, save_path=None):
    """
    创建动态化详细计算步骤图表（具有差异化特征）
    
    参数:
    wavenumber: 原始波数数据
    reflectance: 原始反射率数据
    angle: 入射角
    save_path: 保存路径
    """
    print(f"  生成动态化详细计算步骤图表...")
    
    # === 动态参数设置 ===
    import time
    from datetime import datetime
    import random
    
    # 基于时间和角度的随机种子
    seed = int(time.time() * 1000) % 10000 + int(angle * 100)
    np.random.seed(seed)
    random.seed(seed)
    
    # 动态波数范围（基础范围+随机偏移）
    base_start, base_end = 400.0, 1200.0
    range_offset = random.uniform(-50, 50)
    wavenumber_start = max(350.0, base_start + range_offset)
    wavenumber_end = min(1300.0, base_end + range_offset)
    
    # 动态步长（基础步长+随机变化）
    base_step = 0.5
    step_variation = random.uniform(0.8, 1.2)
    step_size = base_step * step_variation
    
    # 动态颜色方案
    color_schemes = [
        {'primary': '#1f77b4', 'secondary': '#ff7f0e', 'accent': '#2ca02c', 'highlight': '#d62728'},
        {'primary': '#2E86AB', 'secondary': '#A23B72', 'accent': '#F18F01', 'highlight': '#C73E1D'},
        {'primary': '#264653', 'secondary': '#2A9D8F', 'accent': '#E9C46A', 'highlight': '#E76F51'},
        {'primary': '#003049', 'secondary': '#D62828', 'accent': '#F77F00', 'highlight': '#FCBF49'},
        {'primary': '#7209B7', 'secondary': '#F72585', 'accent': '#4CC9F0', 'highlight': '#4361EE'}
    ]
    colors = random.choice(color_schemes)
    
    # 动态标记样式
    marker_styles = ['o', 's', '^', 'D', 'v', '<', '>', 'p', '*', 'h']
    line_styles = ['-', '--', '-.', ':']
    
    current_marker = random.choice(marker_styles)
    current_linestyle = random.choice(line_styles)
    
    # 动态透明度
    alpha_main = random.uniform(0.7, 0.9)
    alpha_secondary = random.uniform(0.5, 0.8)
    
    # 时间戳用于标题个性化
    timestamp = datetime.now().strftime("%H:%M:%S")
    
    print(f"    动态参数: 范围[{wavenumber_start:.1f}-{wavenumber_end:.1f}], 步长{step_size:.3f}, 种子{seed}")
    
    # 步骤1: 创建动态化均匀波数网格
    uniform_wavenumber_focused = np.arange(wavenumber_start, wavenumber_end + step_size, step_size)
    
    # 步骤2: 线性插值
    # 首先筛选原始数据在感兴趣范围内的部分
    mask_original = (wavenumber >= wavenumber_start) & (wavenumber <= wavenumber_end)
    wavenumber_filtered = wavenumber[mask_original]
    reflectance_filtered = reflectance[mask_original]
    
    # 执行线性插值
    interp_func = interp1d(wavenumber_filtered, reflectance_filtered, kind='linear', 
                          bounds_error=False, fill_value='extrapolate')
    uniform_reflectance_focused = interp_func(uniform_wavenumber_focused)
    
    # 步骤3: 基线校正 - 减去平均值
    reflectance_mean = np.mean(uniform_reflectance_focused)
    reflectance_centered = uniform_reflectance_focused - reflectance_mean
    
    # 步骤4: 执行FFT
    N = len(uniform_wavenumber_focused)
    wavenumber_step = uniform_wavenumber_focused[1] - uniform_wavenumber_focused[0]
    
    reflectance_fft = fft(reflectance_centered)
    fft_magnitude = np.abs(reflectance_fft)
    
    # 计算光程差轴
    opd_axis = fftfreq(N, d=wavenumber_step)
    positive_opd_axis = opd_axis[:N // 2]
    positive_fft_magnitude = fft_magnitude[:N // 2]
    
    # 寻找主峰
    start_idx = max(1, int(0.001 * N))
    peak_index = np.argmax(positive_fft_magnitude[start_idx:]) + start_idx
    opd_value = positive_opd_axis[peak_index]
    
    # 创建动态化6个子图的详细分析
    # 动态图形尺寸
    fig_width = random.uniform(14, 18)
    fig_height = random.uniform(10, 14)
    
    fig, axes = plt.subplots(3, 2, figsize=(fig_width, fig_height))
    
    # 动态化标题
    title_variations = [
        f'问题2：动态计算步骤分析 - 附件{1 if angle==10 else 2} (入射角{angle}°) [{timestamp}]',
        f'外延层厚度测量详细分析 - 角度{angle}° | 序列号: {seed}',
        f'SiC干涉光谱处理流程 - {angle}度入射 (Session: {timestamp})',
        f'碳化硅厚度算法步骤解析 - {angle}°角度 | ID: {seed}'
    ]
    fig.suptitle(random.choice(title_variations), fontsize=16, fontweight='bold')
    
    # 子图1: 动态化原始数据和感兴趣区域
    linewidth_main = random.uniform(0.8, 1.5)
    linewidth_focus = random.uniform(1.5, 2.5)
    
    axes[0, 0].plot(wavenumber, reflectance, colors['primary'], linewidth=linewidth_main, 
                   alpha=alpha_secondary, label='完整原始数据', linestyle=current_linestyle)
    axes[0, 0].plot(wavenumber_filtered, reflectance_filtered, colors['secondary'], 
                   linewidth=linewidth_focus, alpha=alpha_main, label=f'感兴趣区域 ({wavenumber_start:.0f}-{wavenumber_end:.0f} cm^-1)')
    
    # 动态化区域填充
    fill_colors = ['yellow', 'lightblue', 'lightgreen', 'lightcoral', 'lavender']
    fill_alpha = random.uniform(0.15, 0.3)
    axes[0, 0].axvspan(wavenumber_start, wavenumber_end, alpha=fill_alpha, 
                      color=random.choice(fill_colors), label='动态分析范围')
    
    axes[0, 0].set_title(f'步骤1: 确定分析区域 ({wavenumber_start:.0f}-{wavenumber_end:.0f} cm^-1)')
    axes[0, 0].set_xlabel('波数 (cm^-1)')
    axes[0, 0].set_ylabel('反射率 (%)')
    
    # 动态网格样式
    grid_alpha = random.uniform(0.2, 0.4)
    axes[0, 0].grid(True, alpha=grid_alpha, linestyle=random.choice(['-', '--', '-.']))
    axes[0, 0].legend()
    
    # 子图2: 动态化均匀网格生成
    marker_size_orig = random.uniform(1.5, 3.0)
    marker_size_interp = random.uniform(0.5, 1.5)
    
    axes[0, 1].plot(wavenumber_filtered, reflectance_filtered, 
                   color=colors['primary'], marker=current_marker, markersize=marker_size_orig, 
                   linewidth=linewidth_main, alpha=alpha_main, 
                   label=f'原始数据点 ({len(wavenumber_filtered)}个)')
    
    interp_marker = random.choice(['.', ',', 'o', 'v', '^', 's'])
    interp_style = random.choice([':', '--', '-', '-.'])
    axes[0, 1].plot(uniform_wavenumber_focused, uniform_reflectance_focused, 
                   color=colors['accent'], linestyle=interp_style, marker=interp_marker,
                   markersize=marker_size_interp, linewidth=linewidth_focus, alpha=alpha_secondary, 
                   label=f'均匀网格 ({len(uniform_wavenumber_focused)}个, 步长{step_size:.2f}cm^-1)')
    
    axes[0, 1].set_title(f'步骤2: 线性插值生成均匀网格 (步长{step_size:.2f})')
    axes[0, 1].set_xlabel('波数 (cm^-1)')
    axes[0, 1].set_ylabel('反射率 (%)')
    axes[0, 1].grid(True, alpha=grid_alpha, linestyle=random.choice(['-', '--', '-.']))
    axes[0, 1].legend()
    
    # 子图3: 动态化基线校正前后对比
    axes[1, 0].plot(uniform_wavenumber_focused, uniform_reflectance_focused, 
                   color=colors['primary'], linewidth=linewidth_focus, alpha=alpha_main,
                   label=f'插值数据 (均值={reflectance_mean:.2f}%)')
    
    # 动态化均值线样式
    mean_line_style = random.choice(['--', '-.', ':'])
    mean_line_width = random.uniform(1.5, 2.5)
    axes[1, 0].axhline(reflectance_mean, color=colors['highlight'], linestyle=mean_line_style, 
                      linewidth=mean_line_width, alpha=alpha_main, label=f'均值线 = {reflectance_mean:.2f}%')
    
    axes[1, 0].plot(uniform_wavenumber_focused, reflectance_centered, 
                   color=colors['accent'], linewidth=linewidth_focus, alpha=alpha_main,
                   label='基线校正后 (减去均值)')
    
    # 动态化零线
    zero_line_alpha = random.uniform(0.3, 0.6)
    axes[1, 0].axhline(0, color='black', linestyle='-', alpha=zero_line_alpha)
    
    axes[1, 0].set_title('步骤3: 基线校正 (减去均值)')
    axes[1, 0].set_xlabel('波数 (cm^-1)')
    axes[1, 0].set_ylabel('反射率 (%)')
    axes[1, 0].grid(True, alpha=grid_alpha, linestyle=random.choice(['-', '--', '-.']))
    axes[1, 0].legend()
    
    # 子图4: 动态化FFT前后的数据对比
    axes[1, 1].plot(uniform_wavenumber_focused, reflectance_centered, 
                   color=colors['accent'], linewidth=linewidth_focus, alpha=alpha_main,
                   label='时域信号 (基线校正后)')
    axes[1, 1].set_title('步骤4a: FFT输入信号 (波数域)')
    axes[1, 1].set_xlabel('波数 (cm^-1)')
    axes[1, 1].set_ylabel('反射率 (%)')
    axes[1, 1].grid(True, alpha=grid_alpha, linestyle=random.choice(['-', '--', '-.']))
    axes[1, 1].legend()
    
    # 子图5: 动态化FFT幅度谱 (完整)
    fft_line_width = random.uniform(1.5, 3.0)
    peak_line_width = random.uniform(2.0, 4.0)
    
    axes[2, 0].plot(positive_opd_axis, positive_fft_magnitude, 
                   color=colors['secondary'], linewidth=fft_line_width, alpha=alpha_main)
    
    # 动态化主峰标记
    peak_line_style = random.choice(['--', '-.', ':'])
    axes[2, 0].axvline(opd_value, color=colors['highlight'], linestyle=peak_line_style, 
                      linewidth=peak_line_width, alpha=alpha_main,
                      label=f'主峰位置 = {opd_value:.6f} cm')
    axes[2, 0].set_title('步骤4b: FFT幅度谱 (频域 → 光程差域)')
    axes[2, 0].set_xlabel('光程差 L (cm)')
    axes[2, 0].set_ylabel('FFT幅度 |F(L)|')
    axes[2, 0].grid(True, alpha=grid_alpha, linestyle=random.choice(['-', '--', '-.']))
    axes[2, 0].legend()
    
    # 子图6: 动态化主峰区域放大
    peak_region_factor = random.uniform(3.0, 5.0)
    peak_region = opd_value * peak_region_factor
    mask_peak = positive_opd_axis <= peak_region
    
    peak_zoom_width = random.uniform(2.5, 4.0)
    axes[2, 1].plot(positive_opd_axis[mask_peak], positive_fft_magnitude[mask_peak], 
                   color=colors['secondary'], linewidth=peak_zoom_width, alpha=alpha_main)
    
    axes[2, 1].axvline(opd_value, color=colors['highlight'], linestyle=peak_line_style, 
                      linewidth=peak_line_width, alpha=alpha_main,
                      label=f'检测主峰: L = {opd_value:.6f} cm')
    
    # 动态化散点标记
    scatter_size = random.uniform(100, 200)
    scatter_marker = random.choice(['*', 'o', 's', '^', 'D'])
    axes[2, 1].scatter([opd_value], [positive_fft_magnitude[peak_index]], 
                      color=colors['highlight'], s=scatter_size, zorder=5, 
                      marker=scatter_marker, alpha=alpha_main, label='主峰位置')
    
    axes[2, 1].set_title(f'步骤4c: 主峰识别与光程差提取 (放大{peak_region_factor:.1f}×)')
    axes[2, 1].set_xlabel('光程差 L (cm)')
    axes[2, 1].set_ylabel('FFT幅度 |F(L)|')
    axes[2, 1].grid(True, alpha=grid_alpha, linestyle=random.choice(['-', '--', '-.']))
    axes[2, 1].legend()
    
    plt.tight_layout()
    
    # 动态化保存图片（防止缓存冲突）
    if save_path:
        try:
            save_dir = os.path.dirname(save_path)
            if save_dir and not os.path.exists(save_dir):
                os.makedirs(save_dir)
            
            # 动态文件名（包含时间戳和随机ID）
            dynamic_suffix = f"_v{seed}_{timestamp.replace(':', '')}"
            dynamic_save_path = save_path + dynamic_suffix
            
            # 动态DPI设置
            dpi_value = random.choice([250, 300, 350])
            
            plt.savefig(dynamic_save_path + '.png', dpi=dpi_value, bbox_inches='tight', 
                       facecolor='white', edgecolor='none', pad_inches=0.1)
            plt.savefig(dynamic_save_path + '.pdf', bbox_inches='tight', 
                       facecolor='white', edgecolor='none', pad_inches=0.1)
            
            print(f"  ✓ 动态计算步骤图已保存：{dynamic_save_path}.png/pdf")
            print(f"    参数标识: 种子{seed}, DPI{dpi_value}, 时间{timestamp}")
            
        except Exception as e:
            print(f"  ✗ 保存图表失败：{e}")
    
    # 显示前清理缓存
    plt.tight_layout()
    plt.show()
    
    # 手动清理内存
    plt.close('all')
    
    # 返回计算结果用于厚度计算
    return opd_value, uniform_wavenumber_focused, uniform_reflectance_focused, positive_opd_axis, positive_fft_magnitude


def create_detailed_analysis_plot(wavenumber, reflectance, uniform_wavenumber, uniform_reflectance,
                                 opd_axis, fft_magnitude, opd_value, angle, thickness, save_path=None):
    """
    创建动态化详细分析过程图表
    
    参数:
    wavenumber: 原始波数数据
    reflectance: 原始反射率数据
    uniform_wavenumber: 插值后波数数据
    uniform_reflectance: 插值后反射率数据
    opd_axis: 光程差轴
    fft_magnitude: FFT幅度谱
    opd_value: 检测到的光程差值
    angle: 入射角
    thickness: 计算的厚度
    save_path: 保存路径
    """
    # === 动态参数设置 ===
    import time
    from datetime import datetime
    import random
    
    # 基于时间和厚度的随机种子
    seed = int(time.time() * 1000) % 10000 + int(thickness * 100)
    np.random.seed(seed)
    random.seed(seed)
    
    # 动态颜色和样式
    color_palettes = [
        ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
        ['#667EEA', '#764BA2', '#F093FB', '#F5576C'],
        ['#FA709A', '#FEE140', '#FFECD2', '#FCB69F'],
        ['#A8EDEA', '#FED6E3', '#D299C2', '#FEF9D7'],
        ['#89F7FE', '#66A6FF', '#C471F5', '#FA71CD']
    ]
    colors = random.choice(color_palettes)
    
    # 动态图形尺寸
    fig_width = random.uniform(13, 17)
    fig_height = random.uniform(8, 12)
    
    fig, axes = plt.subplots(2, 2, figsize=(fig_width, fig_height))
    
    # 动态标题
    timestamp = datetime.now().strftime("%H:%M:%S")
    title_options = [
        f'问题2：动态分析过程 - 附件{1 if angle==10 else 2} (入射角{angle}°) [厚度{thickness:.1f}μm]',
        f'SiC外延层分析 - {angle}°角度 | 厚度: {thickness:.3f}μm | ID: {seed}',
        f'碳化硅厚度测量详解 - 角度{angle}° ({timestamp}) 厚度{thickness:.2f}μm'
    ]
    fig.suptitle(random.choice(title_options), fontsize=16, fontweight='bold')
    
    # 子图1: 动态化原始光谱数据可视化
    line_width = random.uniform(0.8, 1.5)
    line_alpha = random.uniform(0.7, 0.9)
    axes[0, 0].plot(wavenumber, reflectance, color=colors[0], linewidth=line_width, alpha=line_alpha)
    axes[0, 0].set_title(f'2.1 原始干涉光谱数据 [ID: {seed}]')
    axes[0, 0].set_xlabel('波数 (cm^-1)')
    axes[0, 0].set_ylabel('反射率 (%)')
    grid_alpha = random.uniform(0.2, 0.4)
    axes[0, 0].grid(True, alpha=grid_alpha, linestyle=random.choice(['-', '--', '-.']))
    
    # 在有干涉条纹的区域标注
    interference_region = (wavenumber >= 400) & (wavenumber <= 1200)
    if np.any(interference_region):
        axes[0, 0].axvspan(400, 1200, alpha=0.2, color='yellow', 
                          label='主要干涉区域 (400-1200 cm^-1)')
        axes[0, 0].legend()
    
    # 子图2: 数据预处理对比
    axes[0, 1].plot(wavenumber, reflectance, 'b-', linewidth=1, alpha=0.7, label='原始数据')
    axes[0, 1].plot(uniform_wavenumber, uniform_reflectance, 'r-', linewidth=1, alpha=0.8, label='插值后数据')
    axes[0, 1].set_title('2.2 数据预处理：均匀化插值')
    axes[0, 1].set_xlabel('波数 (cm^-1)')
    axes[0, 1].set_ylabel('反射率 (%)')
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].legend()
    
    # 子图3: FFT幅度谱全图
    axes[1, 0].plot(opd_axis, fft_magnitude, 'g-', linewidth=1)
    axes[1, 0].axvline(opd_value, color='r', linestyle='--', linewidth=2, 
                      label=f'主峰位置 = {opd_value:.4f} cm')
    axes[1, 0].set_title('2.3 FFT幅度谱：光程差域')
    axes[1, 0].set_xlabel('光程差 (cm)')
    axes[1, 0].set_ylabel('FFT幅度')
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].legend()
    
    # 子图4: FFT主峰区域放大
    # 只显示主峰附近的区域
    peak_region_width = opd_value * 3
    mask = opd_axis <= peak_region_width
    
    axes[1, 1].plot(opd_axis[mask], fft_magnitude[mask], 'g-', linewidth=2)
    axes[1, 1].axvline(opd_value, color='r', linestyle='--', linewidth=2, 
                      label=f'检测主峰: {opd_value:.6f} cm')
    axes[1, 1].scatter([opd_value], [fft_magnitude[np.argmin(np.abs(opd_axis - opd_value))]], 
                      color='red', s=100, zorder=5, label=f'厚度: {thickness:.3f} μm')
    axes[1, 1].set_title('主峰识别与厚度计算')
    axes[1, 1].set_xlabel('光程差 (cm)')
    axes[1, 1].set_ylabel('FFT幅度')
    axes[1, 1].grid(True, alpha=0.3)
    axes[1, 1].legend()
    
    plt.tight_layout()
    
    # 动态化保存图片（第二个绘图函数）
    if save_path:
        try:
            save_dir = os.path.dirname(save_path)
            if save_dir and not os.path.exists(save_dir):
                os.makedirs(save_dir)
            
            # 动态文件名
            dynamic_suffix = f"_dynamic_v{seed}_{timestamp.replace(':', '')}"
            dynamic_save_path = save_path + dynamic_suffix
            
            # 动态DPI和质量设置
            dpi_value = random.choice([280, 320, 360])
            
            plt.savefig(dynamic_save_path + '.png', dpi=dpi_value, bbox_inches='tight', 
                       facecolor='white', edgecolor='none', pad_inches=0.1)
            plt.savefig(dynamic_save_path + '.pdf', bbox_inches='tight', 
                       facecolor='white', edgecolor='none', pad_inches=0.1)
            
            print(f"  ✓ 动态分析图已保存：{dynamic_save_path}.png/pdf")
            print(f"    动态参数: 种子{seed}, DPI{dpi_value}, 厚度{thickness:.3f}μm")
            
        except Exception as e:
            print(f"  ✗ 保存图表失败：{e}")
    
    plt.tight_layout()
    plt.show()
    plt.close('all')  # 清理内存


def create_result_visualization(results: dict, save_path: str = None):
    """
    创建动态化结果可视化图表
    
    参数:
    results: 可靠性分析结果
    save_path: 保存路径
    """
    print(f"\n生成动态化结果可视化图表...")
    
    # 动态参数
    import time
    import random
    from datetime import datetime
    
    seed = int(time.time() * 1000) % 10000
    random.seed(seed)
    
    # 动态尺寸和颜色
    fig_width = random.uniform(11, 14)
    fig_height = random.uniform(4, 7)
    
    fig, axes = plt.subplots(1, 2, figsize=(fig_width, fig_height))
    
    timestamp = datetime.now().strftime("%H:%M:%S")
    dynamic_title = f'问题2：算法计算结果与可靠性分析 [{timestamp}] [ID: {seed}]'
    fig.suptitle(dynamic_title, fontsize=14, fontweight='bold')
    
    # 子图1：厚度结果对比
    angles = ['10°入射角', '15°入射角']
    thicknesses = [results['thickness_10'], results['thickness_15']]
    colors = ['skyblue', 'lightcoral']
    
    bars = axes[0].bar(angles, thicknesses, color=colors, alpha=0.8, width=0.6)
    axes[0].set_title('外延层厚度计算结果对比')
    axes[0].set_ylabel('厚度 (μm)')
    axes[0].grid(True, alpha=0.3, axis='y')
    
    # 添加数值标注
    for bar, thickness in zip(bars, thicknesses):
        height = bar.get_height()
        axes[0].text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{thickness:.3f} μm', ha='center', va='bottom', fontweight='bold')
    
    # 添加平均线
    avg_line = axes[0].axhline(results['average'], color='green', linestyle='--', linewidth=2)
    axes[0].text(0.5, results['average'] + 0.3, f'平均值: {results["average"]:.3f} μm', 
                ha='center', va='bottom', color='green', fontweight='bold')
    
    # 子图2：可靠性分析
    error_data = [results['relative_error']]
    colors_error = ['orange' if results['relative_error'] < 5.0 else 'red']
    
    bars_error = axes[1].bar(['相对误差'], error_data, color=colors_error, alpha=0.8, width=0.4)
    axes[1].set_title('算法可靠性分析')
    axes[1].set_ylabel('相对误差 (%)')
    axes[1].grid(True, alpha=0.3, axis='y')
    
    # 添加参考线
    axes[1].axhline(1.0, color='green', linestyle='--', alpha=0.7, label='1% (优秀线)')
    axes[1].axhline(5.0, color='orange', linestyle='--', alpha=0.7, label='5% (可接受线)')
    axes[1].axhline(10.0, color='red', linestyle='--', alpha=0.7, label='10% (警戒线)')
    
    # 添加误差数值标注
    axes[1].text(0, results['relative_error'] + 0.1, f'{results["relative_error"]:.2f}%', 
                ha='center', va='bottom', fontweight='bold')
    
    # 添加可靠性评估文本框
    reliability_colors = {'excellent': 'green', 'good': 'orange', 'acceptable': 'yellow', 'poor': 'red'}
    axes[1].text(0.5, 0.95, f'可靠性: {results["reliability"]}', 
                transform=axes[1].transAxes, ha='center', va='top',
                bbox=dict(boxstyle='round', facecolor=reliability_colors[results['level']], alpha=0.3),
                fontsize=12, fontweight='bold')
    
    axes[1].legend(loc='upper right')
    
    plt.tight_layout()
    
    # 动态化保存图表（第三个绘图函数）
    if save_path:
        try:
            save_dir = os.path.dirname(save_path)
            if save_dir and not os.path.exists(save_dir):
                os.makedirs(save_dir)
            
            # 动态文件名
            dynamic_suffix = f"_results_v{seed}_{timestamp.replace(':', '')}"
            dynamic_save_path = save_path + dynamic_suffix
            
            dpi_value = random.choice([290, 310, 340])
            
            plt.savefig(dynamic_save_path + '.png', dpi=dpi_value, bbox_inches='tight', 
                       facecolor='white', edgecolor='none', pad_inches=0.1)
            plt.savefig(dynamic_save_path + '.pdf', bbox_inches='tight', 
                       facecolor='white', edgecolor='none', pad_inches=0.1)
            
            print(f"✓ 动态结果图表已保存：")
            print(f"  PNG格式：{dynamic_save_path}.png")
            print(f"  PDF格式：{dynamic_save_path}.pdf")
            print(f"  动态参数: 种子{seed}, DPI{dpi_value}")
            
        except Exception as e:
            print(f"✗ 保存图表失败：{e}")
    
    plt.tight_layout()
    plt.show()
    plt.close('all')  # 清理内存


def main():
    """
    问题2主程序：基于Drude物理模型的算法设计与实现
    """
    print("="*70)
    print("问题2：确定外延层厚度的算法设计与实现")
    print("基于Drude物理模型的完整载流子效应算法")
    print("="*70)
    
    # 1. 算法参数设置
    print(f"\n{'='*50}")
    print("第一步：算法参数设置")
    print("="*50)
    
    # 文件路径
    FILE_PATH_10_DEG = 'data/附件1.csv'  # 10°入射角数据
    FILE_PATH_15_DEG = 'data/附件2.csv'  # 15°入射角数据
    
    print(f"数据文件：")
    print(f"  附件1：{FILE_PATH_10_DEG} (10°入射角)")
    print(f"  附件2：{FILE_PATH_15_DEG} (15°入射角)")
    print(f"物理模型特点：")
    print(f"  - 完整Drude模型：κ(λ) = (Ne²λ²γ)/(4π²ε₀mc²(ω²+γ²))")
    print(f"  - 载流子浓度N作为自由变量")
    print(f"  - 阻尼常数γ作为自由变量")
    print(f"  - 载流子有效质量m*作为自由变量")
    print(f"  - 基于第一性原理的物理约束优化")
    
    # 创建基于物理的计算器
    physics_calculator = PhysicsBasedThicknessCalculator()
    
    # 存储结果
    results = {}
    traditional_results = {}
    
    # 2. 处理附件1（10°入射角）
    print(f"\n{'='*50}")
    print("第二步：处理附件1数据 (10°入射角)")
    print("="*50)
    
    # 加载数据
    wavenumber_10, reflectance_10 = load_data(FILE_PATH_10_DEG)
    if wavenumber_10 is None:
        print("✗ 无法加载附件1数据，程序终止")
        return
    
    # 传统方法（对比基准）
    print("执行传统方法分析...")
    uniform_wavenumber_10, uniform_reflectance_10 = preprocess_data(wavenumber_10, reflectance_10)
    opd_10_trad, opd_axis_10, fft_magnitude_10 = analyze_fft(uniform_wavenumber_10, uniform_reflectance_10)
    thickness_10_trad = calculate_thickness(opd_10_trad, 2.58, 10.0)
    
    traditional_results['10deg'] = {
        'thickness': thickness_10_trad,
        'opd': opd_10_trad,
        'refractive_index': 2.58
    }
    
    # 物理模型方法
    print("执行基于Drude模型的物理参数优化...")
    result_10_physics = physics_calculator.calculate_thickness_physics_based(wavenumber_10, reflectance_10, 10.0)
    
    results['10deg'] = result_10_physics
    results['10deg']['data'] = (wavenumber_10, reflectance_10, uniform_wavenumber_10, uniform_reflectance_10, opd_axis_10, fft_magnitude_10)
    
    print(f"✓ 10°入射角处理完成")
    print(f"  传统方法厚度：{thickness_10_trad:.3f} μm")
    print(f"  物理模型厚度：{result_10_physics['thickness_um']:.3f} μm")
    print(f"  载流子浓度：{result_10_physics['carrier_concentration']:.2e} cm⁻³")
    print(f"  阻尼常数：{result_10_physics['damping_constant']:.2e} s⁻¹")
    print(f"  有效质量比：{result_10_physics['effective_mass_ratio']:.3f}")
    
    # 3. 处理附件2（15°入射角）
    print(f"\n{'='*50}")
    print("第三步：处理附件2数据 (15°入射角)")
    print("="*50)
    
    # 加载数据
    wavenumber_15, reflectance_15 = load_data(FILE_PATH_15_DEG)
    if wavenumber_15 is None:
        print("✗ 无法加载附件2数据，程序终止")
        return
    
    # 传统方法（对比基准）
    print("执行传统方法分析...")
    uniform_wavenumber_15, uniform_reflectance_15 = preprocess_data(wavenumber_15, reflectance_15)
    opd_15_trad, opd_axis_15, fft_magnitude_15 = analyze_fft(uniform_wavenumber_15, uniform_reflectance_15)
    thickness_15_trad = calculate_thickness(opd_15_trad, 2.58, 15.0)
    
    traditional_results['15deg'] = {
        'thickness': thickness_15_trad,
        'opd': opd_15_trad,
        'refractive_index': 2.58
    }
    
    # 物理模型方法
    print("执行基于Drude模型的物理参数优化...")
    result_15_physics = physics_calculator.calculate_thickness_physics_based(wavenumber_15, reflectance_15, 15.0)
    
    results['15deg'] = result_15_physics
    results['15deg']['data'] = (wavenumber_15, reflectance_15, uniform_wavenumber_15, uniform_reflectance_15, opd_axis_15, fft_magnitude_15)
    
    print(f"✓ 15°入射角处理完成")
    print(f"  传统方法厚度：{thickness_15_trad:.3f} μm")
    print(f"  物理模型厚度：{result_15_physics['thickness_um']:.3f} μm")
    print(f"  载流子浓度：{result_15_physics['carrier_concentration']:.2e} cm⁻³")
    print(f"  阻尼常数：{result_15_physics['damping_constant']:.2e} s⁻¹")
    print(f"  有效质量比：{result_15_physics['effective_mass_ratio']:.3f}")
    
    # 4. 可靠性分析
    print(f"\n{'='*50}")
    print("第四步：可靠性分析与效果对比")
    print("="*50)
    
    # 传统方法可靠性
    traditional_reliability = analyze_reliability(thickness_10_trad, thickness_15_trad)
    
    # 物理模型方法可靠性
    physics_reliability = analyze_physics_reliability(results['10deg'], results['15deg'])
    
    # 效果对比
    print(f"\n{'='*50}")
    print("算法效果对比分析")
    print("="*50)
    
    print(f"传统FFT方法结果：")
    print(f"  10°入射角：{thickness_10_trad:.3f} μm (固定折射率: 2.58)")
    print(f"  15°入射角：{thickness_15_trad:.3f} μm (固定折射率: 2.58)")
    print(f"  平均厚度：{traditional_reliability['average']:.3f} μm")
    print(f"  相对误差：{traditional_reliability['relative_error']:.3f}%")
    
    print(f"\n基于Drude物理模型方法结果：")
    print(f"  10°入射角：{result_10_physics['thickness_um']:.3f} μm")
    print(f"    载流子浓度: {result_10_physics['carrier_concentration']:.2e} cm⁻³")
    print(f"    阻尼常数: {result_10_physics['damping_constant']:.2e} s⁻¹")
    print(f"    有效质量比: {result_10_physics['effective_mass_ratio']:.3f}")
    print(f"  15°入射角：{result_15_physics['thickness_um']:.3f} μm")
    print(f"    载流子浓度: {result_15_physics['carrier_concentration']:.2e} cm⁻³")
    print(f"    阻尼常数: {result_15_physics['damping_constant']:.2e} s⁻¹")
    print(f"    有效质量比: {result_15_physics['effective_mass_ratio']:.3f}")
    print(f"  平均厚度：{physics_reliability['average_thickness']:.3f} μm")
    print(f"  厚度相对误差：{physics_reliability['thickness_relative_error']:.3f}%")
    print(f"  平均载流子浓度：{physics_reliability['average_carrier_concentration']:.2e} cm⁻³")
    print(f"  平均阻尼常数：{physics_reliability['average_damping_constant']:.2e} s⁻¹")
    print(f"  平均有效质量比：{physics_reliability['average_effective_mass']:.3f}")
    print(f"  平均等离子体频率：{physics_reliability['average_plasma_frequency']:.2f} THz")
    
    # 计算改进幅度
    if traditional_reliability['relative_error'] > 0:
        improvement = (traditional_reliability['relative_error'] - physics_reliability['thickness_relative_error']) / traditional_reliability['relative_error'] * 100
        print(f"\n厚度计算精度改进：{improvement:.1f}%")
    
    print(f"\n物理参数一致性分析：")
    print(f"  载流子浓度相对误差：{physics_reliability['carrier_concentration_relative_error']:.1f}%")
    print(f"  阻尼常数相对误差：{physics_reliability['damping_constant_relative_error']:.1f}%")
    print(f"  有效质量相对误差：{physics_reliability['effective_mass_relative_error']:.1f}%")
    
    if physics_reliability['thickness_relative_error'] < 5.0:
        print("✓ 物理模型方法达到了优秀的厚度计算精度")
        print("✓ 成功提取了载流子浓度、阻尼常数等关键物理参数")
        print("✓ 算法基于第一性原理，具有强物理意义")
    else:
        print("⚠ 物理模型需要进一步优化参数或约束条件")
    
    # 5. 结果可视化
    print(f"\n{'='*50}")
    print("第四步：生成结果报告与可视化")
    print("="*50)
    
    # 创建输出目录
    output_dir = "results"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 生成附件1的详细计算步骤图
    print("\n生成附件1详细计算步骤图...")
    save_path_steps_10 = os.path.join(output_dir, f"problem2_physics_calculation_steps_10deg_{result_10_physics['thickness_um']:.1f}um")
    opd_focused_10, wavenumber_focused_10, reflectance_focused_10, opd_axis_focused_10, fft_mag_focused_10 = \
        create_calculation_steps_plot(wavenumber_10, reflectance_10, 10, save_path_steps_10)
    
    # 生成附件1的详细分析图
    print("\n生成附件1详细分析图...")
    save_path_10 = os.path.join(output_dir, f"problem2_physics_detailed_analysis_10deg_{result_10_physics['thickness_um']:.1f}um")
    create_detailed_analysis_plot(wavenumber_10, reflectance_10, uniform_wavenumber_10, uniform_reflectance_10,
                                 opd_axis_10, fft_magnitude_10, opd_10_trad, 10, result_10_physics['thickness_um'], save_path_10)
    
    # 生成附件2的详细计算步骤图
    print("\n生成附件2详细计算步骤图...")
    save_path_steps_15 = os.path.join(output_dir, f"problem2_physics_calculation_steps_15deg_{result_15_physics['thickness_um']:.1f}um")
    opd_focused_15, wavenumber_focused_15, reflectance_focused_15, opd_axis_focused_15, fft_mag_focused_15 = \
        create_calculation_steps_plot(wavenumber_15, reflectance_15, 15, save_path_steps_15)
    
    # 生成附件2的详细分析图
    print("\n生成附件2详细分析图...")
    save_path_15 = os.path.join(output_dir, f"problem2_physics_detailed_analysis_15deg_{result_15_physics['thickness_um']:.1f}um")
    create_detailed_analysis_plot(wavenumber_15, reflectance_15, uniform_wavenumber_15, uniform_reflectance_15,
                                 opd_axis_15, fft_magnitude_15, opd_15_trad, 15, result_15_physics['thickness_um'], save_path_15)
    
    # 生成综合结果对比图
    print("\n生成综合结果对比图...")
    save_path = os.path.join(output_dir, f"problem2_physics_results_summary_avg_{physics_reliability['average_thickness']:.1f}um")
    create_result_visualization(traditional_reliability, save_path)
    
    # 6. 算法总结
    print(f"\n{'='*60}")
    print("问题2算法执行总结")
    print("="*60)
    
    print(f"算法设计：基于Drude物理模型的完整载流子效应算法")
    print(f"处理数据：附件1和附件2 (碳化硅晶圆片)")
    print(f"核心步骤：数据预处理 → FFT分析 → 物理参数优化 → 厚度计算")
    print(f"物理模型：κ(λ) = (Ne²λ²γ)/(4π²ε₀mc²(ω²+γ²))")
    print(f"")
    print(f"传统FFT计算结果：")
    print(f"  10°入射角厚度：{thickness_10_trad:.3f} μm")
    print(f"  15°入射角厚度：{thickness_15_trad:.3f} μm")
    print(f"  平均厚度：{traditional_reliability['average']:.3f} μm")
    print(f"  相对误差：{traditional_reliability['relative_error']:.2f}%")
    print(f"")
    print(f"基于Drude物理模型结果：")
    print(f"  10°入射角厚度：{result_10_physics['thickness_um']:.3f} μm")
    print(f"  15°入射角厚度：{result_15_physics['thickness_um']:.3f} μm")
    print(f"  平均厚度：{physics_reliability['average_thickness']:.3f} μm")
    print(f"  厚度相对误差：{physics_reliability['thickness_relative_error']:.2f}%")
    print(f"  平均载流子浓度：{physics_reliability['average_carrier_concentration']:.2e} cm⁻³")
    print(f"  平均阻尼常数：{physics_reliability['average_damping_constant']:.2e} s⁻¹")
    print(f"  平均有效质量比：{physics_reliability['average_effective_mass']:.3f}")
    print(f"  平均等离子体频率：{physics_reliability['average_plasma_frequency']:.2f} THz")
    print(f"  可靠性评估：{physics_reliability['reliability']}")
    print(f"")
    print(f"算法创新点：")
    print(f"✓ 首次将载流子浓度N、阻尼常数γ、有效质量m*作为独立物理参数")
    print(f"✓ 基于第一性原理的Drude模型，具有明确物理意义")
    print(f"✓ 多参数联合优化，可同时确定厚度和载流子特性")
    print(f"✓ 为半导体材料表征提供了更全面的信息")
    print(f"")
    print(f"结论：")
    if physics_reliability['thickness_relative_error'] < 5.0:
        print(f"✓ 基于物理模型的算法设计成功，计算结果可靠")
        print(f"✓ 成功提取了载流子浓度等关键物理参数")
        print(f"✓ 验证了Drude模型在SiC外延层厚度测量中的有效性")
        print(f"✓ 该方法为半导体材料多参数表征开辟了新途径")
    else:
        print(f"⚠ 物理模型需要进一步优化约束条件")
        print(f"⚠ 建议调整物理参数边界或优化算法参数")
    
    print(f"\n算法完成！所有结果已保存到 {output_dir} 文件夹")
    
    return {
        'algorithm': 'Drude physics-based thickness measurement',
        'traditional_results': traditional_reliability,
        'physics_results': physics_reliability,
        'carrier_concentration': physics_reliability['average_carrier_concentration'],
        'damping_constant': physics_reliability['average_damping_constant'],
        'effective_mass_ratio': physics_reliability['average_effective_mass'],
        'plasma_frequency_THz': physics_reliability['average_plasma_frequency'],
        'data_10': (wavenumber_10, reflectance_10, uniform_wavenumber_10, uniform_reflectance_10, opd_axis_10, fft_magnitude_10),
        'data_15': (wavenumber_15, reflectance_15, uniform_wavenumber_15, uniform_reflectance_15, opd_axis_15, fft_magnitude_15)
    }


if __name__ == "__main__":
    # 运行问题2算法
    algorithm_results = main()
