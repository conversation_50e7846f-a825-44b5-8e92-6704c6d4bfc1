# -*- coding: utf-8 -*-
"""
问题2：快速版本 - 确定外延层厚度的算法设计与实现

基于完整的SiC物理模型，但大幅优化计算速度：
1. 减少数据点数：从65536降到8192
2. 简化优化算法：减少迭代次数和种群大小
3. 简化质量评估：只保留核心指标
4. 跳过复杂的可视化：专注于核心计算

预期速度提升：5-10倍
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d
from scipy.fft import fft, fftfreq
from scipy.optimize import differential_evolution
from scipy.constants import c, e, epsilon_0, m_e
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体显示
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class FastSiCPhysicsModel:
    """快速版本的完整SiC物理模型"""
    
    def __init__(self):
        # 物理常数
        self.c = c
        self.e = e
        self.epsilon_0 = epsilon_0
        self.m_e = m_e
        
        # SiC参数
        self.epsilon_inf = 6.7
        self.phonon_params = {
            'TO_frequency_cm': 797.0,
            'LO_frequency_cm': 972.0,
            'oscillator_strength': 1.0,
            'damping_cm': 4.0
        }
    
    def calculate_complete_dielectric_function(self, wavenumber_cm, N_cm3, gamma_s, m_star_ratio, 
                                             phonon_strength=1.0, phonon_damping=4.0):
        """计算完整介电函数"""
        # 转换为角频率
        omega = wavenumber_cm * 2 * np.pi * self.c * 100
        
        # 声子贡献
        omega_TO = 797.0 * 2 * np.pi * self.c * 100
        gamma_phonon = phonon_damping * 2 * np.pi * self.c * 100
        denominator_phonon = omega_TO**2 - omega**2 - 1j * gamma_phonon * omega
        epsilon_phonon = phonon_strength * omega_TO**2 / denominator_phonon
        
        # Drude贡献
        N_m3 = N_cm3 * 1e6
        m_star = m_star_ratio * self.m_e
        omega_p_squared = (N_m3 * self.e**2) / (self.epsilon_0 * m_star)
        denominator_drude = omega**2 + 1j * gamma_s * omega
        epsilon_drude = -omega_p_squared / denominator_drude
        
        # 总介电函数
        epsilon_total = self.epsilon_inf + epsilon_phonon + epsilon_drude
        
        return epsilon_total
    
    def calculate_complex_refractive_index(self, wavenumber_cm, N_cm3, gamma_s, m_star_ratio,
                                         phonon_strength=1.0, phonon_damping=4.0):
        """计算复折射率"""
        epsilon = self.calculate_complete_dielectric_function(
            wavenumber_cm, N_cm3, gamma_s, m_star_ratio, phonon_strength, phonon_damping)
        return np.sqrt(epsilon)


class FastThicknessCalculator:
    """快速厚度计算器"""
    
    def __init__(self):
        self.physics_model = FastSiCPhysicsModel()
    
    def calculate_thickness_fast(self, wavenumber, reflectance, angle_deg):
        """快速厚度计算"""
        print(f"  执行快速物理优化...")
        
        # 1. 快速数据预处理
        uniform_wavenumber, uniform_reflectance = self._preprocess_data_fast(wavenumber, reflectance)
        
        # 2. FFT分析
        opd_initial = self._calculate_opd_fast(uniform_wavenumber, uniform_reflectance)
        print(f"  初始光程差: {opd_initial:.6f} cm")
        
        # 3. 快速优化
        result = self._fast_optimization(uniform_wavenumber, uniform_reflectance, angle_deg, opd_initial)
        
        print(f"  ✓ 快速优化完成:")
        print(f"    厚度: {result['thickness_um']:.3f} μm")
        print(f"    载流子浓度: {result['carrier_concentration']:.2e} cm⁻³")
        print(f"    载流子阻尼: {result['damping_constant']:.2e} s⁻¹")
        print(f"    有效质量比: {result['effective_mass_ratio']:.3f}")
        print(f"    声子强度: {result['phonon_oscillator_strength']:.3f}")
        print(f"    声子阻尼: {result['phonon_damping_cm']:.2f} cm⁻¹")
        print(f"    拟合质量 R²: {result['r_squared']:.4f}")
        
        return result
    
    def _preprocess_data_fast(self, wavenumber, reflectance, num_points=2**13):
        """快速数据预处理"""
        interp_func = interp1d(wavenumber, reflectance, kind='linear', 
                              bounds_error=False, fill_value='extrapolate')
        uniform_wavenumber = np.linspace(wavenumber.min(), wavenumber.max(), num_points)
        uniform_reflectance = interp_func(uniform_wavenumber)
        uniform_reflectance = uniform_reflectance - np.mean(uniform_reflectance)
        return uniform_wavenumber, uniform_reflectance
    
    def _calculate_opd_fast(self, uniform_wavenumber, uniform_reflectance):
        """快速FFT分析"""
        N = len(uniform_wavenumber)
        wavenumber_step = uniform_wavenumber[1] - uniform_wavenumber[0]
        
        reflectance_centered = uniform_reflectance - np.mean(uniform_reflectance)
        reflectance_fft = fft(reflectance_centered)
        fft_magnitude = np.abs(reflectance_fft)
        
        opd_axis = fftfreq(N, d=wavenumber_step)
        positive_opd_axis = opd_axis[:N // 2]
        positive_fft_magnitude = fft_magnitude[:N // 2]
        
        start_idx = max(1, int(0.001 * N))
        peak_index = np.argmax(positive_fft_magnitude[start_idx:]) + start_idx
        opd_value = positive_opd_axis[peak_index]
        
        return opd_value
    
    def _fast_optimization(self, wavenumber, reflectance, angle_deg, opd_initial):
        """快速优化算法"""
        # 设置优化边界
        angle_rad = np.deg2rad(angle_deg)
        n_base = np.sqrt(6.7)  # 基于ε∞
        estimated_thickness_um = opd_initial * 1e4 / (2 * np.sqrt(n_base**2 - np.sin(angle_rad)**2))
        
        bounds = [
            (estimated_thickness_um * 0.8, estimated_thickness_um * 1.2),  # 厚度
            (1e15, 1e19),      # 载流子浓度
            (1e12, 1e14),      # 载流子阻尼
            (0.2, 2.0),        # 有效质量比
            (0.5, 2.0),        # 声子强度
            (2.0, 10.0)        # 声子阻尼
        ]
        
        def objective_function(params):
            d_um, N_cm3, gamma_s, m_star_ratio, phonon_strength, phonon_damping = params
            try:
                theoretical_reflectance = self._calculate_theoretical_reflectance(
                    wavenumber, d_um, N_cm3, gamma_s, m_star_ratio, angle_deg, 
                    phonon_strength, phonon_damping)
                error = np.sqrt(np.mean((reflectance - theoretical_reflectance)**2))
                return error
            except:
                return 1e6
        
        # 快速差分进化
        result_de = differential_evolution(
            objective_function, 
            bounds, 
            seed=42,
            maxiter=30,       # 大幅减少迭代次数
            popsize=8,        # 减小种群
            atol=1e-6,
            tol=1e-6,
            workers=1,
            polish=False
        )
        
        d_opt, N_opt, gamma_opt, m_star_opt, phonon_strength_opt, phonon_damping_opt = result_de.x
        
        # 计算拟合质量
        predicted_reflectance = self._calculate_theoretical_reflectance(
            wavenumber, d_opt, N_opt, gamma_opt, m_star_opt, angle_deg, 
            phonon_strength_opt, phonon_damping_opt)
        
        # 简化的质量评估
        measured_centered = reflectance - np.mean(reflectance)
        predicted_centered = predicted_reflectance - np.mean(predicted_reflectance)
        ss_res = np.sum((measured_centered - predicted_centered)**2)
        ss_tot = np.sum(measured_centered**2)
        r_squared = max(0, 1 - (ss_res / ss_tot)) if ss_tot > 0 else 0
        
        return {
            'thickness_um': d_opt,
            'carrier_concentration': N_opt,
            'damping_constant': gamma_opt,
            'effective_mass_ratio': m_star_opt,
            'phonon_oscillator_strength': phonon_strength_opt,
            'phonon_damping_cm': phonon_damping_opt,
            'r_squared': r_squared,
            'fitting_error': result_de.fun,
            'optimization_success': result_de.success,
            'predicted_reflectance': predicted_reflectance
        }
    
    def _calculate_theoretical_reflectance(self, wavenumber, d_um, N_cm3, gamma_s, m_star_ratio, angle_deg,
                                         phonon_strength, phonon_damping):
        """计算理论反射率"""
        d_cm = d_um * 1e-4
        angle_rad = np.deg2rad(angle_deg)
        
        # 计算复折射率
        n_complex = self.physics_model.calculate_complex_refractive_index(
            wavenumber, N_cm3, gamma_s, m_star_ratio, phonon_strength, phonon_damping)
        
        # 简化的反射率计算
        n_real = np.real(n_complex)
        sin_theta_t = np.sin(angle_rad) / n_real
        sin_theta_t = np.clip(sin_theta_t, -1, 1)
        cos_theta_t = np.sqrt(1 - sin_theta_t**2)
        
        # 菲涅尔系数
        cos_theta_i = np.cos(angle_rad)
        r01_s = (cos_theta_i - n_complex * cos_theta_t) / (cos_theta_i + n_complex * cos_theta_t)
        r01_p = (n_complex * cos_theta_i - cos_theta_t) / (n_complex * cos_theta_i + cos_theta_t)
        r01 = (r01_s + r01_p) / 2
        
        # 衬底界面
        n_substrate = 3.2
        sin_theta_sub = np.real(n_complex) * sin_theta_t / n_substrate
        sin_theta_sub = np.clip(sin_theta_sub, -1, 1)
        cos_theta_sub = np.sqrt(1 - sin_theta_sub**2)
        
        r12_s = (n_complex * cos_theta_t - n_substrate * cos_theta_sub) / (n_complex * cos_theta_t + n_substrate * cos_theta_sub)
        r12_p = (n_substrate * n_complex * cos_theta_t - n_complex * n_substrate * cos_theta_sub) / (n_substrate * n_complex * cos_theta_t + n_complex * n_substrate * cos_theta_sub)
        r12 = (r12_s + r12_p) / 2
        
        # 相位厚度
        wavelength_cm = 1 / wavenumber
        beta = 2 * np.pi * n_complex * d_cm * cos_theta_t / wavelength_cm
        
        # Fabry-Perot公式
        numerator = r01 + r12 * np.exp(-2j * beta)
        denominator = 1 + r01 * r12 * np.exp(-2j * beta)
        r_total = numerator / denominator
        
        theoretical_reflectance = np.abs(r_total)**2 * 100
        theoretical_reflectance = np.clip(theoretical_reflectance, 0, 100)
        theoretical_reflectance = theoretical_reflectance - np.mean(theoretical_reflectance)
        
        return theoretical_reflectance


def load_data_fast(file_path: str) -> tuple:
    """快速加载数据"""
    encodings = ['gbk', 'gb2312', 'utf-8', 'latin-1']
    
    for encoding in encodings:
        try:
            if file_path.endswith('.xlsx'):
                data = pd.read_excel(file_path)
            else:
                data = pd.read_csv(file_path, encoding=encoding)
            
            wavenumber = data.iloc[:, 0].values
            reflectance = data.iloc[:, 1].values
            
            # 移除无效数据
            valid_mask = ~(np.isnan(wavenumber) | np.isnan(reflectance))
            wavenumber = wavenumber[valid_mask]
            reflectance = reflectance[valid_mask]
            
            print(f"✓ 加载数据: {len(wavenumber)} 点, 波数范围: {wavenumber.min():.0f}-{wavenumber.max():.0f} cm⁻¹")
            return wavenumber, reflectance
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            continue
    
    raise ValueError(f"无法读取文件 {file_path}")


def main_fast():
    """快速版本主程序"""
    print("="*60)
    print("问题2：快速版本 - SiC外延层厚度确定")
    print("基于完整物理模型的快速算法")
    print("="*60)
    
    # 文件路径
    file_path_10_deg = "data/附件1.csv"
    file_path_15_deg = "data/附件2.csv"
    
    calculator = FastThicknessCalculator()
    results = {}
    
    # 处理10°数据
    print(f"\n📊 处理附件1 (10°入射角)")
    print("-" * 40)
    
    try:
        wavenumber_10, reflectance_10 = load_data_fast(file_path_10_deg)
        result_10 = calculator.calculate_thickness_fast(wavenumber_10, reflectance_10, 10.0)
        results['10deg'] = result_10
    except Exception as e:
        print(f"❌ 处理附件1失败: {e}")
    
    # 处理15°数据
    print(f"\n📊 处理附件2 (15°入射角)")
    print("-" * 40)
    
    try:
        wavenumber_15, reflectance_15 = load_data_fast(file_path_15_deg)
        result_15 = calculator.calculate_thickness_fast(wavenumber_15, reflectance_15, 15.0)
        results['15deg'] = result_15
    except Exception as e:
        print(f"❌ 处理附件2失败: {e}")
    
    # 结果分析
    if '10deg' in results and '15deg' in results:
        print(f"\n" + "="*50)
        print("快速结果汇总")
        print("="*50)
        
        thickness_10 = results['10deg']['thickness_um']
        thickness_15 = results['15deg']['thickness_um']
        avg_thickness = (thickness_10 + thickness_15) / 2
        relative_error = abs(thickness_10 - thickness_15) / avg_thickness * 100
        
        print(f"📋 厚度计算结果:")
        print(f"  10°入射角: {thickness_10:.3f} μm")
        print(f"  15°入射角: {thickness_15:.3f} μm")
        print(f"  平均厚度: {avg_thickness:.3f} μm")
        print(f"  相对误差: {relative_error:.3f}%")
        
        print(f"\n🔬 物理参数 (平均值):")
        avg_N = (results['10deg']['carrier_concentration'] + results['15deg']['carrier_concentration']) / 2
        avg_gamma = (results['10deg']['damping_constant'] + results['15deg']['damping_constant']) / 2
        avg_m = (results['10deg']['effective_mass_ratio'] + results['15deg']['effective_mass_ratio']) / 2
        avg_phonon_s = (results['10deg']['phonon_oscillator_strength'] + results['15deg']['phonon_oscillator_strength']) / 2
        avg_phonon_d = (results['10deg']['phonon_damping_cm'] + results['15deg']['phonon_damping_cm']) / 2
        
        print(f"  载流子浓度: {avg_N:.2e} cm⁻³")
        print(f"  载流子阻尼: {avg_gamma:.2e} s⁻¹")
        print(f"  有效质量比: {avg_m:.3f}")
        print(f"  声子振荡器强度: {avg_phonon_s:.3f}")
        print(f"  声子阻尼: {avg_phonon_d:.2f} cm⁻¹")
        
        print(f"\n✅ 快速计算完成！")
        if relative_error < 1.0:
            print(f"  结果一致性: 优秀 (误差 < 1%)")
        elif relative_error < 3.0:
            print(f"  结果一致性: 良好 (误差 < 3%)")
        else:
            print(f"  结果一致性: 需要进一步验证")
    
    return results


if __name__ == "__main__":
    import time
    start_time = time.time()
    results = main_fast()
    end_time = time.time()
    print(f"\n⏱️  总计算时间: {end_time - start_time:.2f} 秒")
